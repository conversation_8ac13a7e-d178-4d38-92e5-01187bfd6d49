'use client';

import { useState } from 'react';

import { supabase } from 'src/lib/supabase';

import { useAuthContext } from 'src/auth/hooks';

import storageService from './storage-service';
import * as supabaseUtils from './supabase-utils';
import { createProductBase, updateProductBase, deleteProductBase } from './product-api';
import {
  updateProductVariant,
  createProductVariant,
  deleteAllProductVariants,
} from './product-variant-service';

/**
 * Tối ưu: Chuyển đổi attributes từ array format sang object format để lưu vào database
 * @param {Array|Object} attributes - Thuộc tính ở dạng array hoặc object
 * @param {string} productType - Loại sản phẩm để xử lý phù hợp
 * @returns {Object} - Thuộc tính ở dạng object
 */
function convertAttributesToObjectFormat(attributes, productType = 'simple') {
  // Tối ưu: Simple products không có attributes
  if (productType === 'simple') {
    return {};
  }

  // Nếu đã là object, trả về nguyên vẹn
  if (!Array.isArray(attributes)) {
    return attributes || {};
  }

  // Nếu là array, chuyển đổi sang object format với validation
  const result = {};
  if (Array.isArray(attributes)) {
    attributes.forEach(attr => {
      if (attr && attr.name && Array.isArray(attr.values) && attr.values.length > 0) {
        result[attr.name] = attr.values;
      }
    });
  }

  return result;
}

/**
 * Hook để thực hiện các thao tác mutation với sản phẩm
 * @returns {Object} - Các hàm mutation
 */

function compareImages(defaultImages = [], currentImages = []) {
  // Đảm bảo defaultImages và currentImages là mảng
  const safeDefaultImages = Array.isArray(defaultImages) ? defaultImages : [];
  const safeCurrentImages = Array.isArray(currentImages) ? currentImages : [];

  // Xác định các hình cần xóa (có trong defaultImages nhưng không còn trong currentImages)
  const imagesToDelete = safeDefaultImages.filter(
    (defaultImg) => {
      // Chỉ xử lý các URL string hợp lệ
      if (typeof defaultImg !== 'string' || !defaultImg.trim()) {
        return false;
      }

      // Kiểm tra xem URL này có còn trong currentImages không
      return !safeCurrentImages.some((currentImg) => {
        // Nếu currentImg là string (URL), so sánh trực tiếp
        if (typeof currentImg === 'string') {
          return currentImg === defaultImg;
        }
        // Nếu currentImg là object, nó là file mới, không phải URL cũ
        return false;
      });
    }
  );

  // Xác định các hình cần upload (các object File trong currentImages)
  const imagesToUpload = safeCurrentImages.filter(
    (img) => 
      // Nếu là object và có thuộc tính file, đó là file cần upload
       typeof img === 'object' && img !== null && (img instanceof File || img.name)
    
  );

  // Xác định các hình giữ nguyên (có trong cả hai mảng)
  const unchangedImages = safeCurrentImages.filter(
    (img) => 
      // Nếu là string và có trong defaultImages
       typeof img === 'string' && safeDefaultImages.includes(img)
    
  );

  console.log('Image comparison result:', {
    defaultImages: safeDefaultImages,
    currentImages: safeCurrentImages,
    toDelete: imagesToDelete,
    toUpload: imagesToUpload,
    unchanged: unchangedImages,
  });

  return {
    toDelete: imagesToDelete,
    toUpload: imagesToUpload,
    unchanged: unchangedImages,
  };
}

export function useProductMutations() {
  const [isMutating, setIsMutating] = useState(false);
  const [error, setError] = useState(null);
  // Lấy thông tin tenant_id từ user hiện tại
  const { user } = useAuthContext();
  const tenantId = user?.app_metadata?.tenant_id || '';
  /**
   * Tạo sản phẩm mới với hình ảnh và biến thể
   * @param {Object} productData - Dữ liệu sản phẩm
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const createProduct = async (productData) => {
    setIsMutating(true);
    setError(null);
    try {
      // Tối ưu: Xác định product type để xử lý phù hợp
      const productType = productData.type || 'simple';
      const isSimpleProduct = productType === 'simple';
      const isVariableProduct = productType === 'variable';

      // Tách dữ liệu sản phẩm, hình ảnh và biến thể
      const { images, variants } = productData;

      // Tối ưu: Validation dữ liệu theo product type
      if (isVariableProduct) {
        if (!productData.attributes || productData.attributes.length === 0) {
          throw new Error('Sản phẩm có biến thể phải có ít nhất một thuộc tính');
        }
      }

      // Tự động tạo SKU nếu không có
      if (!productData.sku || productData.sku.trim() === '') {
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
        productData.sku = `SKU-${timestamp}-${randomSuffix}`;
      }

      // Tự động tạo slug nếu không có
      if (!productData.slug || productData.slug.trim() === '') {
        productData.slug = productData.name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim();
      }

      // Upload multiple images simultaneously using Promise.all
      let uploadResults = [];
      if (images && images.length > 0) {
        // Create an array of upload promises
        const uploadPromises = images
          .filter((image) => typeof image === 'object' && image !== null)
          .map(async (image) => {
            // Generate a unique filename for each image
            const fileName = storageService.generateUniqueFileName(image.name);

            try {
              // Sử dụng storageService để tải lên hình ảnh với tenant isolation
              const filePath = storageService.buildFilePath('products', tenantId, fileName);
              const uploadResult = await storageService.uploadFile('public', filePath, image, {
                upsert: true,
                cacheControl: '3600',
              });

              if (!uploadResult.success) {
                return { success: false, error: uploadResult.error, fileName };
              }

              // Return the upload result
              return {
                success: true,
                publicUrl: uploadResult.publicUrl,
                fileName,
                originalFile: image,
              };
            } catch (uploadError) {
              return { success: false, error: uploadError, fileName };
            }
          });

        // Execute all upload promises simultaneously
        uploadResults = await Promise.all(uploadPromises);

        // Process successful uploads
        const successfulUploads = uploadResults.filter((result) => result.success);

        // Replace file objects with URLs in the images array
        if (successfulUploads.length > 0) {
          productData.images = productData.images.map((img) => {
            // If this is a file that was just uploaded, replace it with its URL
            if (typeof img === 'object' && img !== null) {
              // Find the corresponding upload result
              const uploadResult = uploadResults.find(
                (result) => result.success && result.originalFile === img
              );

              // If found, return the URL, otherwise keep the original
              return uploadResult ? uploadResult.publicUrl : img;
            }

            // Keep strings (URLs) as they are
            return img;
          });
        }
      }

      // Xử lý avatar
      if (productData.avatar && typeof productData.avatar === 'object') {
        const avatarFile = productData.avatar;

        // Kiểm tra trong danh sách hình ảnh đã upload
        if (uploadResults && uploadResults.length > 0) {
          // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
          const matchingUpload = uploadResults.find((result) => {
            if (!result.success) return false;

            // So sánh dựa trên object reference (cùng một object)
            if (result.originalFile === avatarFile) {
              return true;
            }

            // So sánh dựa trên tên file và kích thước nếu có
            if (
              avatarFile.name &&
              result.originalFile.name === avatarFile.name &&
              avatarFile.size &&
              result.originalFile.size === avatarFile.size
            ) {
              return true;
            }

            // So sánh dựa trên path nếu có
            if (avatarFile.path && result.originalFile.path === avatarFile.path) {
              return true;
            }

            return false;
          });

          // Nếu tìm thấy, sử dụng URL từ kết quả upload
          if (matchingUpload) {
            productData.avatar = matchingUpload.publicUrl;
          }
        }
      }
      delete productData.media;
      delete productData.variants;
      delete productData.newVariants;

      // Tối ưu: Convert attributes từ array format sang object format theo product type
      const productDataToSave = {
        ...productData,
        attributes: convertAttributesToObjectFormat(productData.attributes, productType),
        // Tối ưu: Đảm bảo data integrity theo product type
        type: productType,
      };

      // Tối ưu: Validate data trước khi save
      if (isSimpleProduct) {
        // Simple product không được có variants hoặc attributes
        productDataToSave.variants = [];
        productDataToSave.attributes = {};
      }

      // 1. Tạo sản phẩm cơ bản trước
      const productResult = await createProductBase(productDataToSave);

      if (!productResult.success) {
        return productResult;
      }

      const productId = productResult.data[0].id;

      // Mảng chứa các promise để thực hiện song song
      const promises = [];

      // 4. Thêm biến thể sản phẩm nếu có (chỉ cho variable products)
      if (isVariableProduct && variants && variants.length > 0) {
        // Tối ưu: Xử lý tất cả các biến thể với validation
        const variantPromises = variants.map(async (variant) => {
          if (!variant) return null;

          // Xử lý avatar của variant nếu có
          if (variant.avatar && typeof variant.avatar === 'object' && variant.avatar !== null) {
            // Kiểm tra xem avatar có trong danh sách hình ảnh đã upload không
            if (uploadResults && uploadResults.length > 0) {
              // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
              const matchingUpload = uploadResults.find((result) => {
                if (!result.success) return false;

                // So sánh dựa trên object reference (cùng một object)
                if (result.originalFile === variant.avatar) {
                  return true;
                }

                // So sánh dựa trên tên file và kích thước nếu có
                if (
                  variant.avatar.name &&
                  result.originalFile.name === variant.avatar.name &&
                  variant.avatar.size &&
                  result.originalFile.size === variant.avatar.size
                ) {
                  return true;
                }

                // So sánh dựa trên path nếu có
                if (variant.avatar.path && result.originalFile.path === variant.avatar.path) {
                  return true;
                }

                return false;
              });

              // Nếu tìm thấy, sử dụng URL từ kết quả upload
              if (matchingUpload) {
                variant.avatar = matchingUpload.publicUrl;
              }
            }
          }
          // Sử dụng hàm mới để tạo biến thể với thuộc tính JSON
          return createProductVariant({ ...variant, productId });
        });

        promises.push(...variantPromises.filter(Boolean));
      }

      // Thực hiện tất cả các promise
      await Promise.all(promises.filter(Boolean));

      // Đã loại bỏ logic đồng bộ Weaviate để tối ưu hệ thống
      console.log('Product created successfully without Weaviate sync:', productResult.data[0].id);

      // Trả về kết quả thành công với ID sản phẩm
      return {
        success: true,
        data: productResult.data,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Cập nhật sản phẩm
   * @param {string} productId - ID sản phẩm cần cập nhật
   * @param {Object} productData - Dữ liệu sản phẩm
   * @param {Object} defaultValues - Giá trị ban đầu để so sánh thay đổi
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const updateProduct = async (productId, productData, defaultValues = {}) => {
    if (!productId) {
      return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
    }

    if (!productData || typeof productData !== 'object') {
      return { success: false, error: new Error('Dữ liệu sản phẩm không hợp lệ'), data: null };
    }

    setIsMutating(true);
    setError(null);
    try {
      // Tối ưu: Xác định product type để xử lý phù hợp
      const productType = productData.type || defaultValues.type || 'simple';
      const isSimpleProduct = productType === 'simple';
      const isVariableProduct = productType === 'variable';

      // Tối ưu: Validation cho edit mode
      if (isVariableProduct && defaultValues.type === 'variable') {
        // Kiểm tra attributes structure không được thay đổi
        if (defaultValues.attributes && productData.attributes) {
          const originalAttrs = Array.isArray(defaultValues.attributes)
            ? defaultValues.attributes
            : Object.entries(defaultValues.attributes || {}).map(([name, values]) => ({ name, values }));

          const currentAttrs = Array.isArray(productData.attributes)
            ? productData.attributes
            : Object.entries(productData.attributes || {}).map(([name, values]) => ({ name, values }));

          if (originalAttrs.length !== currentAttrs.length) {
            throw new Error('Không thể thay đổi số lượng thuộc tính trong chế độ chỉnh sửa');
          }

          for (let i = 0; i < originalAttrs.length; i++) {
            if (originalAttrs[i].name !== currentAttrs[i].name) {
              throw new Error(`Không thể thay đổi tên thuộc tính "${originalAttrs[i].name}" trong chế độ chỉnh sửa`);
            }
          }
        }
      }
      // So sánh hình ảnh để xác định thay đổi
      console.log('Product data:', productData)
      console.log('Default values:', defaultValues)
      const imageChanges = compareImages(defaultValues.images, productData.images);

      // So sánh avatar để xác định thay đổi
      const avatarChanged = defaultValues.avatar !== productData.avatar;
      const oldAvatar = defaultValues.avatar;

      // Upload multiple images simultaneously using Promise.all
      let uploadResults = [];
      if (imageChanges?.toUpload && imageChanges.toUpload.length > 0) {
        // Create an array of upload promises
        const uploadPromises = imageChanges.toUpload.map(async (image) => {
          // Generate a unique filename for each image
          const fileName = storageService.generateUniqueFileName(image.name);

          try {
            // Sử dụng storageService để tải lên hình ảnh với tenant isolation
            const filePath = storageService.buildFilePath('products', tenantId, fileName);
            const uploadResult = await storageService.uploadFile('public', filePath, image, {
              upsert: true,
              cacheControl: '3600',
            });

            if (!uploadResult.success) {
              return { success: false, error: uploadResult.error, fileName };
            }

            // Return the upload result
            return {
              success: true,
              publicUrl: uploadResult.publicUrl,
              fileName,
              originalFile: image,
            };
          } catch (uploadError) {
            return { success: false, error: uploadError, fileName };
          }
        });

        // Execute all upload promises simultaneously
        uploadResults = await Promise.all(uploadPromises);

        // Process successful uploads
        const successfulUploads = uploadResults.filter((result) => result.success);

        // Add the uploaded images to the product data
        if (successfulUploads.length > 0) {
          // Replace file objects with URLs in the images array
          productData.images = productData.images.map((img) => {
            // If this is a file that was just uploaded, replace it with its URL
            if (typeof img === 'object' && img !== null) {
              // Find the corresponding upload result
              const uploadResult = uploadResults.find(
                (result) => result.success && result.originalFile === img
              );

              // If found, return the URL, otherwise keep the original
              return uploadResult ? uploadResult.publicUrl : img;
            }

            // Keep strings (URLs) as they are
            return img;
          });
        }
      }

      // Xóa hình ảnh cũ không còn sử dụng
      const imagesToDelete = [];
      if (imageChanges?.toDelete && imageChanges.toDelete.length > 0) {
        imagesToDelete.push(...imageChanges.toDelete);
      }

      // Xóa avatar cũ nếu có thay đổi và avatar cũ là URL
      if (avatarChanged && oldAvatar && typeof oldAvatar === 'string' && oldAvatar.trim()) {
        // Kiểm tra xem avatar cũ có còn được sử dụng trong danh sách hình ảnh không
        const avatarStillUsed = productData.images && productData.images.some(img =>
          typeof img === 'string' && img === oldAvatar
        );

        if (!avatarStillUsed) {
          imagesToDelete.push(oldAvatar);
        }
      }

      // Thực hiện xóa hình ảnh từ storage
      if (imagesToDelete.length > 0) {
        console.log('Deleting unused images:', imagesToDelete);
        const deleteResult = await storageService.deleteFilesWithPublicUrl('public', imagesToDelete);

        // Log deletion activity
        try {
          await supabaseUtils.callRPC('log_image_deletion', {
            tenantIdParam: tenantId,
            productIdParam: productId,
            imageUrls: imagesToDelete,
            operationType: 'product_update'
          });
        } catch (logError) {
          console.warn('Failed to log image deletion:', logError);
        }

        if (!deleteResult.success) {
          console.warn('Failed to delete some images:', deleteResult.error);
        } else {
          console.log('Successfully deleted unused images:', imagesToDelete);
        }
      }
      // Xử lý avatar
      if (productData.avatar && typeof productData.avatar === 'object') {
        const avatarFile = productData.avatar;

        // Kiểm tra trong danh sách hình ảnh đã upload
        if (uploadResults && uploadResults.length > 0) {
          // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
          const matchingUpload = uploadResults.find((result) => {
            if (!result.success) return false;

            // So sánh dựa trên object reference (cùng một object)
            if (result.originalFile === avatarFile) {
              return true;
            }

            // So sánh dựa trên tên file và kích thước nếu có
            if (
              avatarFile.name &&
              result.originalFile.name === avatarFile.name &&
              avatarFile.size &&
              result.originalFile.size === avatarFile.size
            ) {
              return true;
            }

            // So sánh dựa trên path nếu có
            if (avatarFile.path && result.originalFile.path === avatarFile.path) {
              return true;
            }

            return false;
          });

          // Nếu tìm thấy, sử dụng URL từ kết quả upload
          if (matchingUpload) {
            productData.avatar = matchingUpload.publicUrl;
          }
        }
      }

      // Tối ưu: Xử lý cập nhật variants - sử dụng variants từ form data
      if (productData.variants && productData.variants.length > 0) {
        // Xử lý cập nhật các variant

        // Tạo mảng các promise để xử lý song song
        const variantPromises = productData.variants.map(async (variant) => {
          if (!variant || !variant.id) return null;

          // Chuẩn bị dữ liệu cập nhật cho variant (chỉ avatar và giá, không cập nhật tồn kho)
          // Tối ưu: Sử dụng camelCase vì updateData sẽ tự động convert sang snake_case
          const variantUpdateData = {
            sellingPrice: variant.sellingPrice,
            costPrice: variant.costPrice,
            sku: variant.sku,
            barcode: variant.barcode,
            isActive: variant.isActive,
            // Không cập nhật stock_quantity để giữ nguyên tồn kho
          };

          // Xử lý avatar nếu có
          if (variant.avatar) {
            // Nếu avatar là file object, cần upload
            if (
              typeof variant.avatar === 'object' &&
              variant.avatar !== null &&
              !variant.avatar.url
            ) {
              // Kiểm tra xem avatar có trong danh sách hình ảnh đã upload không
              if (uploadResults && uploadResults.length > 0) {
                // Tìm kiếm trong kết quả upload xem có hình ảnh nào trùng với avatar không
                const matchingUpload = uploadResults.find((result) => {
                  if (!result.success) return false;

                  // So sánh dựa trên object reference (cùng một object)
                  if (result.originalFile === variant.avatar) {
                    return true;
                  }

                  // So sánh dựa trên tên file và kích thước nếu có
                  if (
                    variant.avatar.name &&
                    result.originalFile.name === variant.avatar.name &&
                    variant.avatar.size &&
                    result.originalFile.size === variant.avatar.size
                  ) {
                    return true;
                  }

                  // So sánh dựa trên path nếu có
                  if (variant.avatar.path && result.originalFile.path === variant.avatar.path) {
                    return true;
                  }

                  return false;
                });

                // Nếu tìm thấy, sử dụng URL từ kết quả upload
                if (matchingUpload) {
                  variant.avatar = matchingUpload.publicUrl;
                }
              }
            }

            // Lưu avatar vào dữ liệu cập nhật
            if (typeof variant.avatar === 'string') {
              // Nếu avatar là URL, lưu vào trường avatar
              variantUpdateData.avatar = variant.avatar;
            }
          }

          // Cập nhật variant trong database
          return updateProductVariant(variant.id, variantUpdateData);
        });

        // Thực hiện tất cả các promise cập nhật variant
        await Promise.all(variantPromises.filter(Boolean));
      }

      delete productData.media;
      delete productData.variants;
      delete productData.newVariants;

      try {
        // Tối ưu: Convert attributes từ array format sang object format theo product type
        const productDataToSave = {
          ...productData,
          attributes: convertAttributesToObjectFormat(productData.attributes, productType),
          // Tối ưu: Đảm bảo data integrity theo product type
          type: productType,
        };

        // Tối ưu: Validate data trước khi save
        if (isSimpleProduct) {
          // Simple product không được có variants hoặc attributes
          productDataToSave.attributes = {};
          // Không xóa variants ở đây vì có thể cần xử lý riêng
        }

        // 1. Cập nhật thông tin cơ bản của sản phẩm
        const productResult = await updateProductBase(productId, productDataToSave);

        if (!productResult.success) {
          return productResult;
        }

        // Đã loại bỏ logic đồng bộ Weaviate để tối ưu hệ thống
        console.log('Product updated successfully without Weaviate sync:', productId);

        return {
          success: true,
          data: productResult.data,
          error: null,
        };
      } catch (updateError) {
        // Kiểm tra nếu lỗi liên quan đến ràng buộc khóa ngoại
        if (
          updateError.message &&
          updateError.message.includes('violates foreign key constraint')
        ) {
          // Thử cập nhật với cách khác để tránh vi phạm ràng buộc khóa ngoại
          // Sử dụng truy vấn SQL trực tiếp để cập nhật sản phẩm
          const { data, error: updateDbError } = await supabase
            .from('products')
            .update({
              name: productData.name,
              slug: productData.slug,
              description: productData.description,
              short_description: productData.shortDescription,
              type: productData.type,
              sku: productData.sku,
              barcode: productData.barcode,
              selling_price: productData.sellingPrice,
              cost_price: productData.costPrice,
              weight: productData.weight,
              length: productData.length,
              width: productData.width,
              height: productData.height,
              attributes: convertAttributesToObjectFormat(productData.attributes),
              is_active: productData.isActive,
              is_featured: productData.isFeatured,
              seo_title: productData.seoTitle,
              seo_description: productData.seoDescription,
              meta_keywords: productData.metaKeywords,
              avatar: productData.avatar,
              images: productData.images,
              updated_at: new Date(),
              // Không cập nhật các trường có thể gây ra vi phạm ràng buộc khóa ngoại
            })
            .eq('id', productId)
            .select();

          if (updateDbError) {
            throw updateDbError;
          }

          return {
            success: true,
            data,
            error: null,
          };
        } else {
          // Nếu không phải lỗi ràng buộc khóa ngoại, ném lại lỗi
          throw updateError;
        }
      }
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Xóa sản phẩm
   * @param {string} productId - ID sản phẩm cần xóa
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const deleteProduct = async (productId) => {
    if (!productId) {
      return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
    }

    setIsMutating(true);
    setError(null);
    try {
      // Lấy thông tin sản phẩm và images
      let productImages = [];
      try {
        const { data } = await supabase
          .from('products')
          .select('images')
          .eq('id', productId)
          .single();

        productImages = data?.images || [];
      } catch (fetchError) {
        console.error('Error fetching product images:', fetchError);
      }

      // Lấy danh sách biến thể của sản phẩm để xóa
      await supabase
        .from('product_variants')
        .select('id')
        .eq('product_id', productId);

      // Xóa tất cả thuộc tính của biến thể và biến thể
      await deleteAllProductVariants(productId);

      // Xóa tất cả hình ảnh sản phẩm từ storage
      if (productImages && productImages.length > 0) {
        await storageService.deleteFilesWithPublicUrl('public', productImages);
      }

      // Đã loại bỏ logic đồng bộ Weaviate để tối ưu hệ thống
      console.log('Product deleted successfully without Weaviate sync:', productId);

      // Xóa sản phẩm
      const result = await deleteProductBase(productId);

      return result;
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  /**
   * Xóa nhiều sản phẩm cùng lúc
   * @param {Array} productIds - Danh sách ID sản phẩm cần xóa
   * @returns {Promise<Object>} - Kết quả từ API
   */
  const bulkDeleteProducts = async (productIds) => {
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return {
        success: false,
        error: new Error('Danh sách ID sản phẩm không hợp lệ'),
        data: null
      };
    }

    setIsMutating(true);
    setError(null);

    try {
      // Gọi API bulk delete
      const response = await fetch('/api/products/bulk-delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ product_ids: productIds }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data: result.results,
        message: result.message,
        error: null,
      };
    } catch (err) {
      setError(err);
      return { success: false, error: err, data: null };
    } finally {
      setIsMutating(false);
    }
  };

  return {
    createProduct,
    updateProduct,
    deleteProduct,
    bulkDeleteProducts,
    isMutating,
    error,
  };
}
