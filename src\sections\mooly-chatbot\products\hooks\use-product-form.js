'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';
import { checkSKUExists } from 'src/actions/mooly-chatbot/product-api';

import { getSchemaByType, getDefaultValuesByType, defaultValues } from '../product-schema';

/**
 * Custom hook để quản lý form sản phẩm với validation tối ưu
 * @param {Object} options - Tùy chọn cấu hình
 * @param {Object} options.currentProduct - Sản phẩm hiện tại (cho chế độ edit)
 * @param {string} options.productId - ID sản phẩm (cho chế độ edit)
 * @param {boolean} options.isEditMode - Chế độ edit hay tạo mới
 * @returns {Object} - Form methods và utilities
 */
export function useProductForm({ currentProduct = null, productId = null, isEditMode = false, initialProductType = 'simple' } = {}) {
  // Tối ưu: Xử lý dữ liệu currentProduct an toàn với phân biệt product type
  const safeCurrentProduct = useMemo(() => {
    if (!currentProduct) return null;

    const normalizeAttributes = (attributes) => {
      // Nếu đã là array, trả về nguyên vẹn
      if (Array.isArray(attributes)) {
        return attributes;
      }

      // Nếu là object, chuyển đổi sang array format
      if (attributes && typeof attributes === 'object') {
        // Chuyển đổi từ {key: [values]} sang [{name: key, values: [values]}]
        return Object.entries(attributes).map(([name, values]) => ({
          name,
          values: Array.isArray(values) ? values : [],
        }));
      }

      // Trường hợp khác, trả về array rỗng
      return [];
    };

    // Tối ưu: Xác định product type để xử lý data phù hợp
    const productType = currentProduct.type || PRODUCT_TYPES.SIMPLE;
    const isSimpleProduct = productType === PRODUCT_TYPES.SIMPLE;
    const isVariableProduct = productType === PRODUCT_TYPES.VARIABLE;

    const safeProduct = {
      ...currentProduct,
      // Đảm bảo type được set đúng
      type: productType,

      // Xử lý arrays
      tags: Array.isArray(currentProduct.tags) ? currentProduct.tags : [],
      gender: isSimpleProduct ? [] : (Array.isArray(currentProduct.gender) ? currentProduct.gender : []),
      images: Array.isArray(currentProduct.images) ? currentProduct.images : [],
      variants: isVariableProduct ? (Array.isArray(currentProduct.variants) ?
        currentProduct.variants.map(variant => ({
          ...variant,
          // Đảm bảo các trường pricing được convert đúng cách
          sellingPrice: variant.sellingPrice || variant.selling_price || 0,
          costPrice: variant.costPrice || variant.cost_price || 0,
          stockQuantity: variant.stockQuantity || variant.stock_quantity || 0,
          isActive: variant.isActive !== undefined ? variant.isActive : (variant.is_active !== undefined ? variant.is_active : true),
        })) : []) : [],
      metaKeywords: isSimpleProduct ? [] : (Array.isArray(currentProduct.metaKeywords) ? currentProduct.metaKeywords : []),

      // Xử lý labels - chỉ cho variable products
      saleLabel: isSimpleProduct ? { enabled: false, content: '' } : (currentProduct.saleLabel || { enabled: false, content: '' }),
      newLabel: isSimpleProduct ? { enabled: false, content: '' } : (currentProduct.newLabel || { enabled: false, content: '' }),

      // Xử lý attributes - quan trọng cho variable products
      attributes: isVariableProduct ? normalizeAttributes(currentProduct.attributes) : [],

      // Đảm bảo các field text có giá trị string
      description: currentProduct.description || '',
      shortDescription: currentProduct.shortDescription || '',
      url: currentProduct.url || '',
      slug: currentProduct.slug || '',
      sku: currentProduct.sku || '',
      seoTitle: isSimpleProduct ? '' : (currentProduct.seoTitle || ''),
      seoDescription: isSimpleProduct ? '' : (currentProduct.seoDescription || ''),

      // Xử lý pricing fields với fallback cho snake_case
      sellingPrice: currentProduct.sellingPrice || currentProduct.selling_price || 0,
      costPrice: isSimpleProduct ? null : (currentProduct.costPrice || currentProduct.cost_price || null),

      // Xử lý dimensions
      weight: isSimpleProduct ? null : (currentProduct.weight || null),
      length: isSimpleProduct ? null : (currentProduct.length || null),
      width: isSimpleProduct ? null : (currentProduct.width || null),
      height: isSimpleProduct ? null : (currentProduct.height || null),

      // Xử lý tax fields
      taxes: isSimpleProduct ? null : (currentProduct.taxes || null),
      includeTaxes: isSimpleProduct ? false : (currentProduct.includeTaxes || false),

      // Xử lý feature flags
      isFeatured: isSimpleProduct ? false : (currentProduct.isFeatured || false),
      trackInventory: currentProduct.trackInventory !== undefined ? currentProduct.trackInventory : false,
      stockQuantity: currentProduct.stockQuantity || 0,
      isActive: currentProduct.isActive !== undefined ? currentProduct.isActive : true,
    };

    return safeProduct;
  }, [currentProduct]);

  // State cho loại sản phẩm hiện tại
  const [currentProductType, setCurrentProductType] = useState(
    safeCurrentProduct?.type || initialProductType || defaultValues.type
  );

  // Tạo schema dựa trên loại sản phẩm - QUAN TRỌNG cho validation đồng bộ
  const currentSchema = useMemo(() => getSchemaByType(currentProductType), [currentProductType]);

  // Lấy default values phù hợp với loại sản phẩm
  const currentDefaultValues = useMemo(() => {
    const baseDefaults = getDefaultValuesByType(currentProductType);
    return {
      ...baseDefaults,
      type: currentProductType,
    };
  }, [currentProductType]);

  // Khởi tạo form với React Hook Form
  const methods = useForm({
    mode: isEditMode ? 'onChange' : 'onBlur', // Edit mode: validate onChange, Create mode: validate onBlur
    resolver: zodResolver(currentSchema),
    defaultValues: isEditMode ? safeCurrentProduct : currentDefaultValues,
    // Sử dụng values để force update form khi dữ liệu thay đổi
    values: isEditMode ? safeCurrentProduct : undefined,
  });

  const {
    watch,
    setValue,
    getValues,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, isValid },
  } = methods;

  // Watch product type để cập nhật schema và reset form khi cần
  const productType = watch('type');

  useEffect(() => {
    if (productType && productType !== currentProductType) {
      setCurrentProductType(productType);

      // Tối ưu: Reset form với default values mới khi thay đổi loại sản phẩm
      // Chỉ reset khi không phải edit mode để tránh mất dữ liệu
      if (!isEditMode) {
        const newDefaultValues = getDefaultValuesByType(productType);
        const currentValues = getValues();

        // Giữ lại các giá trị đã nhập (name, description, categoryId, images, url)
        const preservedValues = {
          name: currentValues.name || '',
          description: currentValues.description || '',
          categoryId: currentValues.categoryId || '',
          images: currentValues.images || [],
          avatar: currentValues.avatar || null,
          url: currentValues.url || '',
        };

        // Reset với default values mới nhưng giữ lại các giá trị quan trọng
        reset({
          ...newDefaultValues,
          ...preservedValues,
          type: productType,
        });
      } else {
        // Tối ưu: Trong edit mode, chỉ cập nhật type mà không reset form
        // Đặc biệt quan trọng cho variable products để giữ nguyên attributes structure
        setValue('type', productType);
      }
    }
  }, [productType, currentProductType, isEditMode, reset, getValues, setValue]);

  // Validate SKU - cho phép SKU trống để tự động tạo
  const validateSKU = useCallback(async (sku) => {
    // Nếu SKU trống, không cần validate (sẽ tự động tạo)
    if (!sku || sku.trim() === '') {
      return; // Cho phép SKU trống
    }

    try {
      const exists = await checkSKUExists(sku, isEditMode ? productId : null);
      if (exists) {
        throw new Error('SKU đã tồn tại. Vui lòng sử dụng SKU khác.');
      }
    } catch (checkError) {
      console.error('Error checking SKU:', checkError);
      throw new Error('Không thể kiểm tra SKU. Vui lòng thử lại.');
    }
  }, [isEditMode, productId]);

  // Tối ưu: Chuẩn bị dữ liệu trước khi submit với xử lý chính xác theo product type
  const prepareSubmitData = useCallback((data) => {
    const productData = { ...data };
    const isSimpleProduct = productType === PRODUCT_TYPES.SIMPLE;
    const isVariableProduct = productType === PRODUCT_TYPES.VARIABLE;

    // Đảm bảo dữ liệu đúng định dạng chung
    productData.sellingPrice = Number(data.sellingPrice) || 0;
    productData.stockQuantity = Number(data.stockQuantity) || 0;
    productData.images = data.images || [];
    productData.isActive = data.isActive !== false;
    productData.trackInventory = data.trackInventory !== false;

    // Tối ưu: Xử lý dữ liệu theo loại sản phẩm với logic rõ ràng
    if (isSimpleProduct) {
      // Sản phẩm đơn giản - chỉ giữ các field cần thiết
      productData.attributes = [];
      productData.variants = [];
      productData.tags = data.tags || [];
      productData.gender = [];
      productData.saleLabel = { enabled: false, content: '' };
      productData.newLabel = { enabled: false, content: '' };
      productData.metaKeywords = [];
      productData.isFeatured = false;
      productData.taxes = null;
      productData.includeTaxes = false;
      productData.costPrice = null;
      productData.weight = null;
      productData.length = null;
      productData.width = null;
      productData.height = null;
      productData.seoTitle = '';
      productData.seoDescription = '';
    } else if (isVariableProduct) {
      // Sản phẩm có biến thể - đảm bảo có attributes và variants
      productData.attributes = data.attributes || [];
      productData.variants = data.variants || [];
      productData.tags = data.tags || [];
      productData.gender = data.gender || [];
      productData.metaKeywords = data.metaKeywords || [];

      // Tối ưu: Validate và xử lý variants có đầy đủ thông tin
      if (productData.variants.length > 0) {
        productData.variants = productData.variants.map(variant => ({
          ...variant,
          sellingPrice: Number(variant.sellingPrice) || productData.sellingPrice,
          stockQuantity: Number(variant.stockQuantity) || 0,
          isActive: variant.isActive !== false,
          // Đảm bảo attributes của variant được format đúng
          attributes: variant.attributes || {},
        }));
      }
    }

    return productData;
  }, [productType]);

  // Tối ưu: Validation bổ sung cho variable product với xử lý edit mode
  const validateVariableProduct = useCallback((data) => {
    if (data.type === PRODUCT_TYPES.VARIABLE) {
      // Kiểm tra attributes
      if (!data.attributes || data.attributes.length === 0) {
        throw new Error('Sản phẩm có biến thể phải có ít nhất một thuộc tính');
      }

      // Kiểm tra mỗi attribute có values
      for (const attr of data.attributes) {
        if (!attr.values || attr.values.length === 0) {
          throw new Error(`Thuộc tính "${attr.name}" phải có ít nhất một giá trị`);
        }
      }

      // Tối ưu: Trong edit mode, kiểm tra attributes structure không được thay đổi
      if (isEditMode && safeCurrentProduct && safeCurrentProduct.attributes) {
        const originalAttributes = safeCurrentProduct.attributes;
        const currentAttributes = data.attributes;

        // Kiểm tra số lượng attributes không được thay đổi
        if (originalAttributes.length !== currentAttributes.length) {
          throw new Error('Không thể thay đổi số lượng thuộc tính trong chế độ chỉnh sửa');
        }

        // Kiểm tra tên attributes không được thay đổi
        for (let i = 0; i < originalAttributes.length; i++) {
          if (originalAttributes[i].name !== currentAttributes[i].name) {
            throw new Error(`Không thể thay đổi tên thuộc tính "${originalAttributes[i].name}" trong chế độ chỉnh sửa`);
          }
        }
      }

      // Kiểm tra variants nếu có
      if (data.variants && data.variants.length > 0) {
        for (const variant of data.variants) {
          if (!variant.sellingPrice || variant.sellingPrice < 0) {
            throw new Error('Tất cả biến thể phải có giá hợp lệ');
          }
        }
      }
    }
  }, [isEditMode, safeCurrentProduct]);

  // Submit handler với validation tự động và đồng bộ theo loại sản phẩm
  const createSubmitHandler = useCallback((onSubmitSuccess, onSubmitError) => handleSubmit(async (data) => {
      try {
        // Validate SKU trước khi submit
        await validateSKU(data.sku);

        // Validation bổ sung cho variable product
        validateVariableProduct(data);

        // Chuẩn bị dữ liệu
        const productData = prepareSubmitData(data);

        // Gọi callback success
        await onSubmitSuccess(productData);
      } catch (error) {
        // Gọi callback error
        onSubmitError(error);
      }
    }), [handleSubmit, validateSKU, validateVariableProduct, prepareSubmitData]);

  // Utilities
  const isSimpleProduct = productType === PRODUCT_TYPES.SIMPLE;
  const hasErrors = Object.keys(errors).length > 0;

  // Auto-generate slug từ name
  const generateSlug = useCallback((name) => {
    if (!name) return '';
    
    return name
      .toLowerCase()
      .trim()
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
      .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
      .replace(/[ìíịỉĩ]/g, 'i')
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
      .replace(/[ùúụủũưừứựửữ]/g, 'u')
      .replace(/[ỳýỵỷỹ]/g, 'y')
      .replace(/đ/g, 'd')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }, []);

  // Auto-generate slug khi name thay đổi
  const productName = watch('name');
  useEffect(() => {
    if (productName && !isEditMode) {
      const slug = generateSlug(productName);
      setValue('slug', slug);
    }
  }, [productName, isEditMode, generateSlug, setValue]);

  // Tối ưu: Helper function để kiểm tra xem có thể chỉnh sửa attributes không
  const canEditAttributes = useCallback(() => {
    // Trong create mode, luôn cho phép chỉnh sửa
    if (!isEditMode) return true;

    // Trong edit mode với simple product, không có attributes
    if (currentProductType === PRODUCT_TYPES.SIMPLE) return false;

    // Trong edit mode với variable product, chỉ cho phép thêm values, không thay đổi structure
    return false; // Không cho phép thay đổi structure
  }, [isEditMode, currentProductType]);

  // Tối ưu: Helper function để kiểm tra xem có thể thêm values cho attribute không
  const canAddAttributeValues = useCallback((attributeName) => {
    if (!isEditMode) return true;
    if (currentProductType !== PRODUCT_TYPES.VARIABLE) return false;

    // Kiểm tra attribute có tồn tại trong original data không
    if (safeCurrentProduct && safeCurrentProduct.attributes) {
      return safeCurrentProduct.attributes.some(attr => attr.name === attributeName);
    }

    return false;
  }, [isEditMode, currentProductType, safeCurrentProduct]);

  return {
    // Form methods
    ...methods,

    // Custom handlers
    createSubmitHandler,

    // Utilities
    isSimpleProduct,
    hasErrors,
    isValid,
    currentProductType,

    // States
    isSubmitting,
    errors,

    // Helpers
    generateSlug,
    prepareSubmitData,
    validateSKU,

    // Tối ưu: Edit mode helpers
    canEditAttributes,
    canAddAttributeValues,
    isEditMode,
  };
}
